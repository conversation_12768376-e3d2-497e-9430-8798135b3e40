<!--
    Create Club Page - USIU Events Management System
    
    Administrative interface for creating and managing student clubs.
    Provides comprehensive form for club registration including leadership
    assignment, media uploads, and organizational details.
    
    Page Features:
    - Club information form with validation
    - Leadership search and assignment
    - Logo upload with preview
    - Category selection and status management
    - User search with role assignment
    
    Form Sections:
    - Basic Information: Name, description, category, contact
    - Club Leadership: Leader search and selection
    - Club Media: Logo upload with preview
    - Club Settings: Status and visibility options
    
    JavaScript Dependencies:
    - admin-clubs.js: Form handling and club creation logic
    - component-loader.js: Navbar component loading
    - utils.js: UI utilities and form validation
    - main.js: General application functionality
    
    Dynamic Features:
    - User search for leadership assignment
    - Real-time search results with user profiles
    - Image upload with preview functionality
    - Form validation with error handling
    - Loading states for async operations
    
    Access Control:
    - Requires administrator privileges
    - User search for eligible club leaders
    - Role assignment and permission management
    
    User Experience:
    - Clear form sections with logical flow
    - Search autocomplete for user selection
    - Visual feedback for all interactions
    - Responsive design for all devices
    - Save options with status management
-->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Club - USIU Events</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="../../assets/css/style.css">
</head>
<body class="bg-gray-50">
    <div id="navbar-placeholder"></div>

    <!-- Page Content -->
    <div class="pt-16 min-h-screen">
        <!-- Page Header -->
        <div class="bg-white shadow-sm border-b">
            <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 id="main-title" class="text-2xl font-bold text-gray-900">Create New Club</h1>
                        <p class="mt-1 text-sm text-gray-600">Fill in the details below to create a new student club</p>
                    </div>
                    <div class="flex space-x-3">
                        <a href="./admin-dashboard.html" class="bg-gray-600 text-white rounded-md px-4 py-2 text-sm font-medium hover:bg-gray-700">
                            Cancel
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Club Form -->
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <form id="club-form" class="space-y-8">
                <!-- Basic Information -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-medium text-gray-900 mb-6">Basic Information</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="md:col-span-2">
                            <label for="name" class="block text-sm font-medium text-gray-700">Club Name *</label>
                            <input type="text" id="name" name="name" required
                                   class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="Enter club name"
                                   minlength="3" maxlength="100">
                        </div>
                        
                        <div class="md:col-span-2">
                            <label for="description" class="block text-sm font-medium text-gray-700">Description *</label>
                            <textarea id="description" name="description" rows="4" required
                                      class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                      placeholder="Describe what your club is about, its mission, and activities..."
                                      minlength="10" maxlength="1000"></textarea>
                        </div>
                        
                        <div>
                            <label for="category" class="block text-sm font-medium text-gray-700">Category *</label>
                            <select id="category" name="category" required
                                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                <option value="">Select a category</option>
                                <option value="Arts & Culture">Arts & Culture</option>
                                <option value="Academic">Academic</option>
                                <option value="Sports">Sports</option>
                                <option value="Technology">Technology</option>
                                <option value="Business">Business</option>
                                <option value="Community Service">Community Service</option>
                                <option value="Religious">Religious</option>
                                <option value="Professional">Professional</option>
                                <option value="Recreation">Recreation</option>
                                <option value="Special Interest">Special Interest</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="members_count" class="block text-sm font-medium text-gray-700">Initial Members Count</label>
                            <input type="number" id="members_count" name="members_count" min="0" max="10000"
                                   class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="0">
                            <p class="mt-1 text-xs text-gray-500">Current number of members (optional)</p>
                        </div>
                        
                        <div class="md:col-span-2">
                            <label for="contact_email" class="block text-sm font-medium text-gray-700">Contact Email *</label>
                            <input type="email" id="contact_email" name="contact_email" required
                                   class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="<EMAIL>"
                                   maxlength="100">
                        </div>
                    </div>
                </div>

                <!-- Leadership -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-medium text-gray-900 mb-6">Club Leadership</h2>
                    <div class="grid grid-cols-1 gap-6">
                        <div>
                            <label for="leader_search" class="block text-sm font-medium text-gray-700">Club Leader *</label>
                            
                            <!-- Current Leader Info (shown in edit mode) -->
                            <div id="current_leader_info" class="hidden mt-2 p-3 bg-green-50 border border-green-200 rounded-md">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <img id="current_leader_avatar" src="" alt="" class="w-8 h-8 rounded-full object-cover">
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-green-900" id="current_leader_name"></p>
                                        <p class="text-xs text-green-700" id="current_leader_email"></p>
                                    </div>
                                    <div class="ml-auto">
                                        <span class="text-xs text-green-600">Current leader</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="relative mt-1">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                    </svg>
                                </div>
                                <input id="leader_search" type="text" placeholder="Search and select a club leader..." 
                                       class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" 
                                       required autocomplete="off">
                                <input type="hidden" id="leader_id" name="leader_id" required>
                                
                                <!-- Dropdown Results -->
                                <div id="leader_dropdown" class="hidden absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
                                    <div id="leader_results" class="py-1">
                                        <!-- User search results will appear here -->
                                    </div>
                                    <div id="leader_loading" class="hidden px-4 py-3 text-sm text-gray-500 text-center">
                                        <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mx-auto mb-2"></div>
                                        Loading users...
                                    </div>
                                    <div id="leader_no_results" class="hidden px-4 py-3 text-sm text-gray-500 text-center">
                                        No users found. Try a different search term.
                                    </div>
                                </div>
                            </div>
                            <p class="mt-1 text-xs text-gray-500">The selected user will be granted club leader permissions</p>
                        </div>
                    </div>
                </div>

                <!-- Club Media -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-medium text-gray-900 mb-6">Club Logo</h2>
                    
                    <div>
                        <label for="logo" class="block text-sm font-medium text-gray-700">Club Logo</label>
                        <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-gray-400 transition-colors">
                            <div class="space-y-1 text-center">
                                <div id="logo-preview" class="hidden">
                                    <img id="logo-preview-img" src="" alt="Logo preview" class="mx-auto h-24 w-24 rounded-lg object-cover">
                                </div>
                                <div id="logo-upload-area">
                                    <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                    <div class="flex text-sm text-gray-600">
                                        <label for="logo" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500">
                                            <span>Upload a logo</span>
                                            <input id="logo" name="logo" type="file" accept="image/*" class="sr-only">
                                        </label>
                                        <p class="pl-1">or drag and drop</p>
                                    </div>
                                    <p class="text-xs text-gray-500">PNG, JPG, GIF up to 5MB</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Club Settings -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-medium text-gray-900 mb-6">Club Settings</h2>
                    
                    <div class="space-y-4">
                        <div>
                            <label for="status" class="block text-sm font-medium text-gray-700">Club Status</label>
                            <select id="status" name="status"
                                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                            </select>
                            <p class="mt-1 text-xs text-gray-500">Inactive clubs are not visible to students</p>
                        </div>
                    </div>
                </div>

                <!-- Error/Success Messages -->
                <div id="form-message" class="hidden">
                    <div id="error-message" class="hidden bg-red-50 border border-red-200 rounded-md p-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-red-800">Error</h3>
                                <div class="mt-2 text-sm text-red-700">
                                    <p id="error-text"></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="success-message" class="hidden bg-green-50 border border-green-200 rounded-md p-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-green-800">Success</h3>
                                <div class="mt-2 text-sm text-green-700">
                                    <p id="success-text"></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                    <a href="./admin-dashboard.html" class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Cancel
                    </a>
                    <button type="button" id="save-inactive" class="bg-gray-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed">
                        <span id="inactive-text">Save as Inactive</span>
                        <div id="inactive-spinner" class="hidden">
                            <svg class="animate-spin -ml-1 mr-3 h-4 w-4 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Saving...
                        </div>
                    </button>
                    <button type="submit" id="submit-button" class="bg-blue-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed">
                        <span id="submit-text">Create Club</span>
                        <div id="submit-spinner" class="hidden">
                            <svg class="animate-spin -ml-1 mr-3 h-4 w-4 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            <span id="submit-spinner-text">Creating...</span>
                        </div>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../../assets/js/utils.js" type="module"></script>
    <script src="../../assets/js/main.js" type="module"></script>
    <script src="../../assets/js/component-loader.js" type="module"></script>
    <script src="../../assets/js/admin/admin-clubs.js" type="module"></script>
</body>
</html>
