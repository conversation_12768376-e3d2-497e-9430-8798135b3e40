# USIU Events Management System - Environment Configuration
# Copy this file to .env and update with your actual values

# MongoDB Configuration
# Get your MongoDB connection string from MongoDB Atlas or local installation
MONGODB_URI=mongodb://localhost:27017
MONGODB_DB=usiu_events

# JWT Configuration
# Generate a secure random string for JWT token signing
# You can use: openssl rand -base64 32
JWT_SECRET=your_jwt_secret_key_here

# AWS S3 Configuration (for file uploads)
# Get these from your AWS Console > IAM > Users > Security Credentials
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
AWS_BUCKET=your_s3_bucket_name

# SMTP Configuration (for email notifications)
# You can use Gmail, Mailtrap, SendGrid, or any SMTP service
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
SMTP_FROM_EMAIL=<EMAIL>
SMTP_FROM_NAME=USIU Events System

# Frontend URL (for CORS and email links)
# Update this to match your frontend URL
FRONTEND_URL=http://localhost:3000
