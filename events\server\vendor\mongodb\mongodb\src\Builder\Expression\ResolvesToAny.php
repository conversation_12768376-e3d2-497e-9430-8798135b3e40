<?php

/**
 * THIS FILE IS AUTO-GENERATED. ANY CHANGES WILL BE LOST!
 */

declare(strict_types=1);

namespace MongoDB\Builder\Expression;

interface ResolvesToAny extends ResolvesToDouble, ResolvesToString, ResolvesToObject, ResolvesToArray, ResolvesToBinData, ResolvesToObjectId, ResolvesToBool, ResolvesToDate, ResolvesToNull, ResolvesToRegex, ResolvesToJavascript, ResolvesToInt, ResolvesToTimestamp, ResolvesToLong, ResolvesToDecimal, ResolvesToNumber
{
}
