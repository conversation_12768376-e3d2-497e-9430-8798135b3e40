{"$schema": "http://cyclonedx.org/schema/bom-1.5.schema.json", "bomFormat": "CycloneDX", "specVersion": "1.5", "serialNumber": "urn:uuid:dc42a43b-4ace-4c42-9a6e-0b9e28fdd100", "version": 1, "metadata": {"timestamp": "2024-05-08T09:51:01Z", "tools": [{"name": "composer", "version": "2.7.6"}, {"vendor": "cyclonedx", "name": "cyclonedx-php-composer", "version": "v5.2.0", "externalReferences": [{"type": "distribution", "url": "https://api.github.com/repos/CycloneDX/cyclonedx-php-composer/zipball/f3a3cdc1a9e34bf1d5748e4279a24569cbf31fed", "comment": "dist reference: f3a3cdc1a9e34bf1d5748e4279a24569cbf31fed"}, {"type": "vcs", "url": "https://github.com/CycloneDX/cyclonedx-php-composer.git", "comment": "source reference: f3a3cdc1a9e34bf1d5748e4279a24569cbf31fed"}, {"type": "website", "url": "https://github.com/CycloneDX/cyclonedx-php-composer/#readme", "comment": "as detected from Composer manifest 'homepage'"}, {"type": "issue-tracker", "url": "https://github.com/CycloneDX/cyclonedx-php-composer/issues", "comment": "as detected from Composer manifest 'support.issues'"}, {"type": "vcs", "url": "https://github.com/CycloneDX/cyclonedx-php-composer/", "comment": "as detected from Composer manifest 'support.source'"}]}, {"vendor": "cyclonedx", "name": "cyclonedx-library", "version": "v3.3.1", "externalReferences": [{"type": "distribution", "url": "https://api.github.com/repos/CycloneDX/cyclonedx-php-library/zipball/cad0f92b36c85f36b3d3c11ff96002af5f20cd10", "comment": "dist reference: cad0f92b36c85f36b3d3c11ff96002af5f20cd10"}, {"type": "vcs", "url": "https://github.com/CycloneDX/cyclonedx-php-library.git", "comment": "source reference: cad0f92b36c85f36b3d3c11ff96002af5f20cd10"}, {"type": "website", "url": "https://github.com/CycloneDX/cyclonedx-php-library/#readme", "comment": "as detected from Composer manifest 'homepage'"}, {"type": "documentation", "url": "https://cyclonedx-php-library.readthedocs.io", "comment": "as detected from Composer manifest 'support.docs'"}, {"type": "issue-tracker", "url": "https://github.com/CycloneDX/cyclonedx-php-library/issues", "comment": "as detected from Composer manifest 'support.issues'"}, {"type": "vcs", "url": "https://github.com/CycloneDX/cyclonedx-php-library/", "comment": "as detected from Composer manifest 'support.source'"}]}]}}