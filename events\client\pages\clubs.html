<!--
    Student Clubs Listing Page - USIU Events Management System
    
    Comprehensive directory of student clubs and organizations with advanced
    filtering, search, and dual view modes (grid/list). Users can discover,
    filter, and join campus clubs through this interface.
    
    Page Features:
    - Advanced search and filtering (category, status, size)
    - Dual view modes: grid and list layouts
    - Club discovery with detailed information cards
    - Join functionality with authentication checks
    - Pagination with "Load More" approach
    - Responsive design with mobile optimization
    
    Filter Options:
    - Category: Arts & Culture, Academic, Sports, Technology, etc.
    - Status: Active, Recruiting (membership open)
    - Size: Small (1-25), Medium (26-100), Large (100+)
    - Sort: Name, member count, creation date
    
    JavaScript Dependencies:
    - clubs.js: Main page functionality and club management
    - component-loader.js: Navbar component loading
    - auth.js: Authentication state management
    - utils.js: UI utilities and helper functions
    - api.js: API communication for club data
    
    Interactive Elements:
    - Search input with debounced filtering
    - Multi-select filter dropdowns
    - Grid/list view toggle buttons
    - Club cards with click-to-view functionality
    - Join buttons with loading states
    
    Responsive Design:
    - Mobile-first approach with collapsible filters
    - Adaptive grid layouts (1-2-3-4 columns)
    - Touch-friendly interactive elements
    - Sticky filter bar for easy access while scrolling
-->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clubs - USIU Events</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <div id="navbar-placeholder"></div>
    

    <!-- Page Header -->
    <section class="bg-gradient-to-r from-blue-600 to-purple-600 pt-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="text-center">
                <h1 class="text-3xl md:text-4xl font-bold text-white mb-4">Student Clubs</h1>
                <p class="text-lg text-gray-200 max-w-2xl mx-auto">
                    Join vibrant communities of like-minded students. Discover clubs that match your interests, develop new skills, and make lasting friendships.
                </p>
            </div>
        </div>
    </section>

    <!-- Search and Filters -->
    <section class="bg-white shadow-sm border-b sticky top-16 z-40">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div class="flex flex-col lg:flex-row gap-4 items-center">
                <!-- Search Bar -->
                <div class="flex-1 w-full lg:w-auto">
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                        <input id="search-input" type="text" placeholder="Search clubs..." 
                               class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>
                
                <!-- Filter Buttons -->
                <div class="flex flex-wrap gap-2">
                    <!-- Category Filter -->
                    <select id="category-filter" class="px-3 py-2 border border-gray-300 rounded-md bg-white text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">All Categories</option>
                        <option value="Arts & Culture">Arts & Culture</option>
                        <option value="Academic">Academic</option>
                        <option value="Sports">Sports</option>
                        <option value="Technology">Technology</option>
                        <option value="Business">Business</option>
                        <option value="Community Service">Community Service</option>
                        <option value="Religious">Religious</option>
                        <option value="Professional">Professional</option>
                        <option value="Recreation">Recreation</option>
                        <option value="Special Interest">Special Interest</option>
                    </select>
                    
                    <!-- Status Filter -->
                    <select id="status-filter" class="px-3 py-2 border border-gray-300 rounded-md bg-white text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">All Clubs</option>
                        <option value="active">Active</option>
                        <option value="recruiting">Recruiting</option>
                    </select>
                    
                    <!-- Size Filter -->
                    <select id="size-filter" class="px-3 py-2 border border-gray-300 rounded-md bg-white text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">All Sizes</option>
                        <option value="small">Small (1-25 members)</option>
                        <option value="medium">Medium (26-100 members)</option>
                        <option value="large">Large (100+ members)</option>
                    </select>
                    
                    <!-- Clear Filters -->
                    <button id="clear-filters" class="px-3 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-md hover:bg-gray-50">
                        Clear Filters
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Clubs Grid -->
    <section class="py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Notification Messages -->
            <div id="error-message" class="hidden bg-red-50 border border-red-200 rounded-md p-3 mb-6">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p id="error-text" class="text-sm text-red-800"></p>
                    </div>
                </div>
            </div>

            <div id="success-message" class="hidden bg-green-50 border border-green-200 rounded-md p-3 mb-6">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p id="success-text" class="text-sm text-green-800"></p>
                    </div>
                </div>
            </div>

            <!-- Results Info -->
            <div class="flex justify-between items-center mb-6">
                <div>
                    <p id="results-count" class="text-gray-600">Loading clubs...</p>
                </div>
                <div class="flex items-center space-x-2">
                    <label for="sort-by" class="text-sm text-gray-600">Sort by:</label>
                    <select id="sort-by" class="px-3 py-1 border border-gray-300 rounded-md bg-white text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                        <option value="name-asc">Name (A-Z)</option>
                        <option value="name-desc">Name (Z-A)</option>
                        <option value="members-desc">Most Members</option>
                        <option value="members-asc">Least Members</option>
                        <option value="newest">Newest First</option>
                        <option value="oldest">Oldest First</option>
                    </select>
                </div>
            </div>

            <!-- View Toggle -->
            <div class="flex justify-end mb-6">
                <div class="flex border border-gray-300 rounded-md">
                    <button id="grid-view" class="px-3 py-1 text-sm bg-blue-600 text-white rounded-l-md">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
                        </svg>
                    </button>
                    <button id="list-view" class="px-3 py-1 text-sm bg-white text-gray-700 rounded-r-md border-l">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Clubs Grid View -->
            <div id="clubs-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                
            </div>

            <!-- Clubs List View -->
            <div id="clubs-list" class="hidden space-y-4">
                <!-- List items will be populated here -->
            </div>

            <!-- No Results Message -->
            <div id="no-results" class="hidden text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No clubs found</h3>
                <p class="mt-1 text-sm text-gray-500">Try adjusting your search criteria or filters.</p>
            </div>

            <!-- Load More Button -->
            <div id="load-more-container" class="text-center mt-8">
                <button id="load-more-btn" class="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
                    <span id="load-more-text">Load More Clubs</span>
                    <div id="load-more-spinner" class="hidden">
                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Loading...
                    </div>
                </button>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center mb-4">
                        <img src="../assets/images/logo.png" alt="USIU Events" class="h-8 w-auto">
                        <span class="ml-2 text-xl font-bold">USIU Events</span>
                    </div>
                    <p class="text-gray-400">
                        Your gateway to amazing campus experiences at United States International University.
                    </p>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-2">
                        <li><a href="../index.html" class="text-gray-400 hover:text-white">Home</a></li>
                        <li><a href="./events.html" class="text-gray-400 hover:text-white">Events</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">Clubs</a></li>
                        <li><a href="../index.html#about" class="text-gray-400 hover:text-white">About</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">Account</h3>
                    <ul class="space-y-2">
                        <li><a href="./login.html" class="text-gray-400 hover:text-white">Login</a></li>
                        <li><a href="./register.html" class="text-gray-400 hover:text-white">Register</a></li>
                        <li><a href="./dashboard.html" class="text-gray-400 hover:text-white">Dashboard</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">Contact</h3>
                    <ul class="space-y-2">
                        <li class="text-gray-400"><EMAIL></li>
                        <li class="text-gray-400">+254 123 456 789</li>
                        <li class="text-gray-400">USIU Campus, Nairobi</li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-800 mt-8 pt-8 text-center">
                <p class="text-gray-400">&copy; 2025 USIU Events. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="../assets/js/utils.js" type="module"></script>
    <script src="../assets/js/api.js" type="module"></script>
    <script src="../assets/js/auth.js" type="module"></script>
    <script src="../assets/js/component-loader.js" type="module"></script>
    <script src="../assets/js/main.js" type="module"></script>
    <script src="../assets/js/clubs.js" type="module"></script>
</body>
</html>
